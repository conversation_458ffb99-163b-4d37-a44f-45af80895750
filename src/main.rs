use anyhow::{Result, anyhow};
use dpn_peernode_lib::daemon::{<PERSON>eer<PERSON>ode<PERSON>ae<PERSON>, PeerNodeDaemonConfig};
use dpn_peernode_lib::types::AuthTokens;
use dpn_peernode_lib::integration::sentry::init_sentry;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

#[tokio::main(flavor = "multi_thread", worker_threads = 10)]
async fn main() -> Result<()> {
    env_logger::init_from_env(env_logger::Env::new().default_filter_or("info"));

    // Initialize Sentry - keep the guard in scope for the entire program
    let _sentry_guard = init_sentry();

    // Add a test event to verify Sen<PERSON> is working
    sentry::add_breadcrumb(sentry::Breadcrumb {
        category: Some("startup".into()),
        message: Some("Peernode daemon starting".into()),
        level: sentry::Level::Info,
        ..Default::default()
    });

    // Log a test message
    // sentry::capture_message("Testing Sentry integration from main function", sentry::Level::Info);

    let config = parse_config()?;

    let daemon = Arc::new(PeerNodeDaemon::new(PeerNodeDaemonConfig {
        admin_addr: config.admin_addr,
        webserver_addr: config.websocket_addr,
        webserver_port: config.websocket_port,
        auth_tokens: AuthTokens {
            access_token: config.access_token,
            refresh_token: config.refresh_token,
        },
    }));

    // Create a transaction for the entire daemon lifecycle
    let transaction = sentry::start_transaction(
        sentry::TransactionContext::new(
            "daemon_lifecycle",
            "startup"
        )
    );

    // Set the transaction as current
    sentry::configure_scope(|scope| {
        scope.set_transaction(Some("daemon_lifecycle"));
    });

    // Run the daemon with websocket
    let result = daemon.run_with_websocket().await;

    // Finish the transaction
    transaction.finish();

    result
}

#[derive(Deserialize, Serialize, Debug, Clone)]
struct Config {
    websocket_addr: String,
    websocket_port: u16,
    admin_addr: String,
    access_token: String,
    refresh_token: String,
}

fn parse_config() -> Result<Config> {
    if std::path::Path::new("./config.yaml").exists() {
        let mut file = std::fs::File::open("./config.yaml")?;
        let mut contents = String::new();
        std::io::Read::read_to_string(&mut file, &mut contents)?;
        let config = serde_yaml::from_str::<Config>(&contents)?;
        Ok(config)
    } else {
        panic!("failed to parse config file")
    }
}
