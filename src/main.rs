use anyhow::Result;
use dpn_peernode_lib::daemon::direct::{DirectPeerNodeDaemon, DirectPeerNodeDaemonConfig};
// use dpn_peernode_lib::daemon::{PeerNodeDaemon, PeerNodeDaemonConfig};
use dpn_peernode_lib::types::AuthTokens;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

use dpn_bridge::api;

#[tokio::main(flavor = "multi_thread", worker_threads = 10)]
async fn main() -> Result<()> {
    env_logger::init_from_env(env_logger::Env::new().default_filter_or("info"));

    println!("=== PeerNode Bridge API Test ===\n");

    let config = parse_config()?;

     // Test 1: Initialize PeerNode
     println!("1. Initializing PeerNode...");
     let controller_id = api::initialize_peer_node(
         config.admin_addr.clone(),
         config.access_token.clone(),
         config.refresh_token.clone(),
     );
     println!("   ✓ Controller initialized with ID: {}", controller_id);

    let direct_daemon = Arc::new(DirectPeerNodeDaemon::new(DirectPeerNodeDaemonConfig {
        admin_addr: config.admin_addr,
        auth_tokens: AuthTokens { 
            access_token: config.access_token,
            refresh_token: config.refresh_token,
        },
    }));

    _ = direct_daemon.run_proxy_loop().await;
    // _ = daemon.run_with_websocket().await;

    Ok(())
}

#[derive(Deserialize, Serialize, Debug, Clone)]
struct Config {
    websocket_addr: String,
    websocket_port: u16,
    admin_addr: String,
    access_token: String,
    refresh_token: String,
}

fn parse_config() -> Result<Config> {
    if std::path::Path::new("./config.yaml").exists() {
        let mut file = std::fs::File::open("./config.yaml")?;
        let mut contents = String::new();
        std::io::Read::read_to_string(&mut file, &mut contents)?;
        let config = serde_yaml::from_str::<Config>(&contents)?;
        Ok(config)
    } else {
        panic!("failed to parse config file")
    }
}
