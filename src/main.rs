use anyhow::Result;
use dpn_peernode_lib::daemon::{PeerNodeDaemon, PeerNodeDaemonConfig};
use dpn_peernode_lib::types::AuthTokens;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

#[tokio::main(flavor = "multi_thread", worker_threads = 10)]
async fn main() -> Result<()> {
    env_logger::init_from_env(env_logger::Env::new().default_filter_or("info"));

    let config = parse_config()?;

    let daemon = Arc::new(PeerNodeDaemon::new(PeerNodeDaemonConfig {
        admin_addr: config.admin_addr,
        webserver_addr: config.websocket_addr,
        webserver_port: config.websocket_port,
        auth_tokens: AuthTokens {
            access_token: config.access_token,
            refresh_token: config.refresh_token,
        },
    }));

    // _ = daemon.run().await;
   _ = daemon.run_with_websocket().await;

    Ok(())
}

#[derive(Deserialize, Serialize, Debug, Clone)]
struct Config {
    websocket_addr: String,
    websocket_port: u16,
    admin_addr: String,
    access_token: String,
    refresh_token: String,
}

fn parse_config() -> Result<Config> {
    if std::path::Path::new("./config.yaml").exists() {
        let mut file = std::fs::File::open("./config.yaml")?;
        let mut contents = String::new();
        std::io::Read::read_to_string(&mut file, &mut contents)?;
        let config = serde_yaml::from_str::<Config>(&contents)?;
        Ok(config)
    } else {
        panic!("failed to parse config file")
    }
}
