use crate::core::connector::DirectConnector;
use crate::core::proxy_server::ProxyServer;
use crate::types::AuthTokens;
use anyhow::Result;
use futures::stream::SplitSink;
use futures::{SinkExt, StreamExt};
use log::{error, info};
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::Duration;
use tokio::net::{TcpListener, TcpStream};
use tokio::sync::mpsc::{UnboundedReceiver, UnboundedSender};
use tokio::sync::Mutex;
use tokio::sync::RwLock;
use tokio_tungstenite::tungstenite::protocol::Message;
use tokio_tungstenite::tungstenite::Error;
use tokio_tungstenite::{accept_async, WebSocketStream};

pub mod event;

use self::event::DaemonEvent;
use crate::{
    core::{start, ConnErrResult},
    integration::admin::AdminServiceImpl,
};
#[derive(Debug, Clone)]
pub struct PeerNodeDaemonConfig {
    pub webserver_addr: String,
    pub webserver_port: u16,
    pub admin_addr: String,
    pub auth_tokens: AuthTokens,
}

pub struct PeerNodeDaemon {
    config: PeerNodeDaemonConfig,
    auth_tokens: Arc<RwLock<AuthTokens>>,
    admin_svc: Arc<AdminServiceImpl>,
    proxy_running: RwLock<bool>,
    shutdown_proxy_tx: UnboundedSender<()>,
    shutdown_proxy_rx: Arc<Mutex<UnboundedReceiver<()>>>,
    proxy_server: Arc<RwLock<Option<Arc<ProxyServer<DirectConnector>>>>>,
}

impl PeerNodeDaemon {
    pub fn new(config: PeerNodeDaemonConfig) -> Self {
        let auth_tokens = Arc::new(RwLock::new(config.auth_tokens.clone()));

        let admin_svc = Arc::new(AdminServiceImpl::new(
            config.admin_addr.clone(),
            auth_tokens.clone(),
        ));

        let (shutdown_proxy_tx, shutdown_proxy_rx) = tokio::sync::mpsc::unbounded_channel::<()>();

        Self {
            config: config.clone(),
            auth_tokens: auth_tokens.clone(),
            admin_svc,
            proxy_running: RwLock::new(false),
            shutdown_proxy_tx,
            shutdown_proxy_rx: Arc::new(Mutex::new(shutdown_proxy_rx)),
            proxy_server: Arc::new(RwLock::new(None)),
        }
    }

    pub async fn run(self: Arc<Self>) -> Result<()> {
        info!("running daemon...");
        let _self = self.clone();
        let _self2 = self.clone();
        loop {
            let admin_svc = _self2.clone().admin_svc.clone();

            info!("ASSIGNING_MASTERNODE");
            match admin_svc.clone().assign_masternode().await {
                Ok(masternode) => {
                    info!("RUNNING_PROXY_SERVER");
                    let auth_tokens = admin_svc.clone().get_auth_tokens().await;
                    if let Err(e) = start(auth_tokens.clone(), masternode).await {
                        error!("PROXY_SERVER_ERR={:#?}", e);
                        match e {
                            ConnErrResult::InvalidAuth => {
                                match _self2.clone().admin_svc.clone().refresh_token().await {
                                    Ok(_) => {
                                        info!("REFRESHED_AUTH_TOKENS");
                                    }
                                    Err(e) => {
                                        error!("REFRESHED_AUTH_TOKENS_FAILED={}", e)
                                    }
                                }
                            }
                            _ => {}
                        };
                        info!("RECONNECTING");
                        tokio::time::sleep(Duration::from_secs(5)).await;
                        continue;
                    }
                }
                Err(e) => {
                    error!("ASSIGNING_MASTERNODE_ERR={:#?}", e);
                    tokio::time::sleep(Duration::from_secs(5)).await;
                    continue;
                }
            }
        }
    }

    pub async fn run_with_websocket(self: Arc<Self>) -> Result<()> {
        info!("running daemon with websocket...");

        let config = self.config.clone();
        let addr =
            format!("{}:{}", config.webserver_addr, config.webserver_port).parse::<SocketAddr>()?;
        let listener = TcpListener::bind(&addr).await?;
        info!("ws server is listening on: {}", addr);

        while let Ok((stream, addr)) = listener.accept().await {
            let _self = self.clone();
            tokio::spawn(async move {
                _ = _self.handle_ws_connection(stream).await;
            });
            info!("ws connection established client_addr={}", addr.to_string())
        }

        Ok(())
    }

    async fn handle_ws_connection(
        self: Arc<Self>,
        stream: tokio::net::TcpStream,
    ) -> Result<(), Error> {
        // Accept the WebSocket connection
        let ws_stream = accept_async(stream)
            .await
            .expect("Error during WebSocket handshake");

        // Split the WebSocket stream
        let (tx, mut rx) = ws_stream.split();
        let safe_tx = Arc::new(RwLock::new(tx));

        // Handle messages from the client
        while let Some(Ok(message)) = rx.next().await {
            let tx = safe_tx.clone();
            if let Message::Text(message) = message {
                let _self = self.clone();
                if let Ok(event) = serde_json::from_str::<DaemonEvent>(&message) {
                    tokio::spawn(async move {
                        _self.clone().handle_ws_event(tx.clone(), event).await;
                    });
                } else {
                    error!("failed to decode daemon event");
                    _self
                        .clone()
                        .emit_ws_event(
                            tx.clone(),
                            DaemonEvent::MessageParsingError(format!(
                                "failed to decode daemon event message={}",
                                message
                            )),
                        )
                        .await;
                }
            }
        }

        Ok(())
    }

    async fn handle_ws_event(
        self: Arc<Self>,
        ws_tx: Arc<RwLock<SplitSink<WebSocketStream<TcpStream>, Message>>>,
        event: DaemonEvent,
    ) {
        // Add a breadcrumb for each event
        sentry::add_breadcrumb(sentry::Breadcrumb {
            category: Some("websocket".into()),
            message: Some(format!("Received event: {:?}", event)),
            level: sentry::Level::Info,
            ..Default::default()
        });
        match event {
            DaemonEvent::StartConnection(auth_tokens) => {
                sentry::add_breadcrumb(sentry::Breadcrumb {
                    category: Some("connection".into()),
                    message: Some("Starting connection with auth tokens".into()),
                    level: sentry::Level::Info,
                    ..Default::default()
                });
                let mut at_wlock = self.auth_tokens.write().await;
                at_wlock.access_token = auth_tokens.access_token;
                at_wlock.refresh_token = auth_tokens.refresh_token;
                drop(at_wlock);
                tokio::spawn(async move { self.clone().start_proxy_server(ws_tx).await });
            }
            DaemonEvent::StopConnection() => {
                sentry::add_breadcrumb(sentry::Breadcrumb {
                    category: Some("connection".into()),
                    message: Some("Stopping connection".into()),
                    level: sentry::Level::Info,
                    ..Default::default()
                });
                tokio::spawn(async move { self.clone().stop_proxy_server(ws_tx).await });
            }
            e => {
                sentry::add_breadcrumb(sentry::Breadcrumb {
                    category: Some("connection".into()),
                    message: Some(format!("Unsupported event: {:?}", e)),
                    level: sentry::Level::Error,
                    ..Default::default()
                });
                error!("unsupported event: {:#?}", e)
            }
        };
    }

    async fn emit_ws_event(
        self: Arc<Self>,
        ws_tx: Arc<RwLock<SplitSink<WebSocketStream<TcpStream>, Message>>>,
        event: DaemonEvent,
    ) {
        info!("Event: {:#?}", event);
        let mut tx_guard = ws_tx.write().await;
        if let Ok(json_event) = serde_json::to_string(&event) {
            _ = tx_guard.send(Message::Text(json_event)).await;
        }
    }

    async fn start_proxy_server(
        self: Arc<Self>,
        ws_tx: Arc<RwLock<SplitSink<WebSocketStream<TcpStream>, Message>>>,
    ) {
        let _self = self.clone();
        let _self2 = self.clone();
        let ws_tx2 = ws_tx.clone();
        let proxy_running = _self.proxy_running.read().await;
        let running = proxy_running.clone();
        drop(proxy_running);
        if running == false {
            let _self3 = _self2.clone();
            let _self4 = _self2.clone();
            let _self5: Arc<PeerNodeDaemon> = _self2.clone();
            let _self6: Arc<PeerNodeDaemon> = _self2.clone();

            let mut shutdown_proxy_rx = _self4.shutdown_proxy_rx.lock().await;

            tokio::select! {
                _ = shutdown_proxy_rx.recv() => {
                    info!("shutting down proxy server");
                    let ps_guard = _self5.proxy_server.read().await;
                    if let Some(proxy_server) = ps_guard.as_ref() {
                        info!("stopping proxy server");
                        proxy_server.clone().force_stop().await;
                    }
                    drop(ps_guard);
                    _self4
                    .clone()
                    .emit_ws_event(
                        ws_tx.clone(),
                        DaemonEvent::ConnectionStopped("PROXY_SERVER_SHUTTED_DOWN".to_string()),
                    )
                    .await;
                },
                _ = async move {

                    let ws_tx = ws_tx2.clone();
                    let admin_svc = _self3.clone().admin_svc.clone();

                    _self3
                        .clone()
                        .emit_ws_event(
                            ws_tx.clone(),
                            DaemonEvent::Info("ASSIGNING_MASTERNODE".to_string()),
                        )
                        .await;
                    match admin_svc.clone().assign_masternode().await {
                        Ok(masternode) => {
                            _self3
                                .clone()
                                .emit_ws_event(
                                    ws_tx.clone(),
                                    DaemonEvent::Info("RUNNING_PROXY_SERVER".to_string()),
                                )
                                .await;
                            let auth_tokens = admin_svc.clone().get_auth_tokens().await;
                            match start(auth_tokens.clone(), masternode).await {
                                Ok(proxy_server) => {
                                    // Store first
                                    info!("writing proxy server to proxy_server");
                                    let mut ps_guard = _self3.proxy_server.write().await;
                                    *ps_guard = Some(proxy_server.clone());
                                    drop(ps_guard);

                                    // Then run it
                                    tokio::spawn(async move {
                                        if let Err(e) = proxy_server.run().await {
                                            // set proxy_running to false
                                            let mut proxy_running = _self6.proxy_running.write().await;
                                            *proxy_running = false;
                                            drop(proxy_running);

                                            // notify client
                                            if e.to_string() == "connection closed due to error: closed" {
                                                _self6
                                                .clone()
                                                .emit_ws_event(
                                                    ws_tx.clone(),
                                                    DaemonEvent::ConnectionStopped("PROXY_SERVER_SHUTTED_DOWN".to_string()),
                                                )
                                                .await;
                                            }else{
                                                _self6
                                                .clone()
                                                .emit_ws_event(
                                                    ws_tx.clone(),
                                                    DaemonEvent::ConnectionStopped(format!(
                                                        "PROXY_SERVER_ERR=TimeOut",
                                                    )),
                                                )
                                                .await;
                                            }

                                            // error!("Proxy server error: {}", e);
                                        }
                                    });

                                    // Set running state to true
                                    let mut proxy_running = _self3.proxy_running.write().await;
                                    *proxy_running = true;
                                    drop(proxy_running);
                                },
                                Err(e) => {
                                    match e {
                                        ConnErrResult::InvalidAuth => {
                                            match _self3.clone().admin_svc.clone().refresh_token().await {
                                                Ok(_) => {
                                                    _self3
                                                    .clone()
                                                    .emit_ws_event(
                                                        ws_tx.clone(),
                                                        DaemonEvent::ConnectionStopped(
                                                            "REFRESHED_AUTH_TOKENS".to_string(),
                                                        ),
                                                    )
                                                    .await;
                                                    // reset proxy_running to false
                                                    let mut proxy_running = _self3.proxy_running.write().await;
                                                    *proxy_running = false;
                                                    drop(proxy_running);
                                                }
                                                Err(e) => {
                                                    _self3
                                                        .clone()
                                                        .emit_ws_event(
                                                            ws_tx.clone(),
                                                            DaemonEvent::ConnectionStopped(format!(
                                                                "REFRESHED_AUTH_TOKENS_FAILED={}",
                                                                e
                                                            )),
                                                        )
                                                        .await;
                                                    // reset proxy_running to false
                                                    let mut proxy_running = _self3.proxy_running.write().await;
                                                    *proxy_running = false;
                                                    drop(proxy_running);
                                                }
                                            }
                                        }
                                        _ => {
                                            _self3
                                                .clone()
                                                .emit_ws_event(
                                                    ws_tx.clone(),
                                                    DaemonEvent::ConnectionStopped(format!(
                                                        "ASSIGNING_MASTERNODE_ERR={:#?}",
                                                        e
                                                    )),
                                                )
                                                .await;
                                            // reset proxy_running to false
                                            let mut proxy_running = _self3.proxy_running.write().await;
                                            *proxy_running = false;
                                            drop(proxy_running);
                                        }
                                    }
                                }
                            }
                        }
                        Err(e) => {
                            _self3
                                .clone()
                                .emit_ws_event(
                                    ws_tx.clone(),
                                    DaemonEvent::ConnectionStopped(format!(
                                        "ASSIGNING_MASTERNODE_ERR={:#?}",
                                        e
                                    )),
                                )
                                .await;
                            // reset proxy_running to false
                            let mut proxy_running = _self3.proxy_running.write().await;
                            *proxy_running = false;
                            drop(proxy_running);
                        }
                    }
                } => {},
            }
        } else {
            info!("proxy server already running");
        };
    }

    async fn stop_proxy_server(
        self: Arc<Self>,
        ws_tx: Arc<RwLock<SplitSink<WebSocketStream<TcpStream>, Message>>>,
    ) {
        let _self = self.clone();
        let proxy_running = _self.proxy_running.read().await;
        let running = proxy_running.clone();
        drop(proxy_running);

        if running {
            // Get the proxy server
            let ps_guard = self.proxy_server.read().await;
            if let Some(proxy_server) = ps_guard.as_ref() {
                // Call stop on it
                proxy_server.clone().force_stop().await;
                info!("sent stop signal to proxy server");
            }
            drop(ps_guard);

            // Also send shutdown signal to our local task
            // match self.shutdown_proxy_tx.clone().send(()) {
            //     Ok(_) => info!("sent shutdown signal to proxy task"),
            //     Err(e) => error!("error sending shutdown signal to proxy task: {}", e),
            // };

            // Reset proxy server
            let mut ps_guard: tokio::sync::RwLockWriteGuard<
                '_,
                Option<Arc<ProxyServer<DirectConnector>>>,
            > = self.proxy_server.write().await;
            *ps_guard = None;
            drop(ps_guard);

            // reset proxy_running to false
            let mut proxy_running = _self.proxy_running.write().await;
            *proxy_running = false;
            drop(proxy_running);

            // Notify client
            // self.clone()
            //     .emit_ws_event(
            //         ws_tx,
            //         DaemonEvent::ConnectionStopped("CONNECTION_STOPPED".to_string()),
            //     )
            //     .await;
        } else {
            info!("no connection is running");
            self.clone()
                .emit_ws_event(
                    ws_tx,
                    DaemonEvent::ConnectionStopped("NO_CONNECTION_IS_RUNNING".to_string()),
                )
                .await;
        }
    }
}
