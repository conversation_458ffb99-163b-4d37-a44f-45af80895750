use std::sync::Arc;

use anyhow::{anyhow,Error, Result};
use reqwest::{Client, StatusCode};
use tokio::sync::RwLock;

use crate::types::{AssignMasternodeRes, AuthTokens, MasternodeInfo, SSORes};

#[derive(Debug)]
pub struct AdminServiceImpl {
    auth_tokens: Arc<RwLock<AuthTokens>>,

    pub base_url: String,
    // paths
    assign_masternode_path: String,
    refresh_token_path: String,
}

impl AdminServiceImpl {
    pub fn new(base_url: String, auth_tokens: Arc<RwLock<AuthTokens>>) -> Self {
        let base_url: String = match base_url.strip_suffix('/') {
            Some(url) => url.to_string(),
            None => base_url.clone(),
        };

        Self {
            auth_tokens,
            base_url: base_url.clone(),
            assign_masternode_path: format!(
                "{}/{}",
                base_url.clone(),
                "connections/assign_masternode"
            ),
            refresh_token_path: format!("{}/{}", base_url.clone(), "auth/refresh_token"),
        }
    }

    pub async fn assign_masternode(self: Arc<Self>) -> Result<MasternodeInfo, Error> {
        let _self = self.clone();
        let client = self.get_client().await;

        // Add a breadcrumb before the request
        sentry::add_breadcrumb(sentry::Breadcrumb {
            category: Some("http".into()),
            message: Some(format!(
                "Requesting masternode assignment from {}",
                _self.assign_masternode_path
            )),
            level: sentry::Level::Info,
            ..Default::default()
        });

        match client
            .get(_self.assign_masternode_path.clone())
            .send()
            .await
            {
                Ok(res) => {
                    // Add a breadcrumb for the response
                    sentry::add_breadcrumb(sentry::Breadcrumb {
                        category: Some("http".into()),
                        message: Some(format!("Received response with status: {}", res.status())),
                        level: sentry::Level::Info,
                        ..Default::default()
                    });

                    match res.status() {
                        StatusCode::OK => match res.json::<AssignMasternodeRes>().await {
                            Ok(assign_masternode_rs) => match assign_masternode_rs.masternode {
                                Some(masternode) => return Ok(masternode),
                                None => {
                                    // Capture the error with context
                                    sentry::with_scope(|scope| {
                                        scope.set_tag("operation", "assign_masternode");
                                        scope.set_extra("url", serde_json::Value::String(_self.assign_masternode_path.clone()));
                                        scope.set_extra("error_type", serde_json::Value::String("no_masternode_online".to_string()));
                                        scope.set_level(Some(sentry::Level::Error));
                                    }, || {
                                        sentry::capture_message("No masternode is online", sentry::Level::Error);
                                    });
                                    return Err(anyhow!("no masternode is online"));
                                },
                            },
                            Err(e) => {
                                // Capture the JSON decoding error with context
                                sentry::with_scope(|scope| {
                                    scope.set_tag("operation", "assign_masternode");
                                    scope.set_extra("url", serde_json::Value::String(_self.assign_masternode_path.clone()));
                                    scope.set_extra("error_type", serde_json::Value::String("json_decode_error".to_string()));
                                    scope.set_extra("error_details", serde_json::Value::String(e.to_string()));
                                    scope.set_level(Some(sentry::Level::Error));
                                }, || {
                                    sentry::capture_message(&format!("Failed to decode masternode JSON: {}", e), sentry::Level::Error);
                                });
                                return Err(anyhow!("decode masternode json failed"));
                            },
                        },
                        StatusCode::UNAUTHORIZED => {
                            // Capture the unauthorized error
                            sentry::with_scope(|scope| {
                                scope.set_tag("operation", "assign_masternode");
                                scope.set_extra("url", serde_json::Value::String(_self.assign_masternode_path.clone()));
                                scope.set_extra("error_type", serde_json::Value::String("unauthorized".to_string()));
                                scope.set_level(Some(sentry::Level::Error));
                            }, || {
                                sentry::capture_message("Unauthorized to assign masternode", sentry::Level::Error);
                            });
                            _ = _self.refresh_token().await;
                            return Err(anyhow!("unauthorized to assign masternode"));
                        }
                        _ => {
                            // Capture unknown status code error
                            sentry::with_scope(|scope| {
                                scope.set_tag("operation", "assign_masternode");
                                scope.set_extra("url", serde_json::Value::String(_self.assign_masternode_path.clone()));
                                scope.set_extra("error_type", serde_json::Value::String("unknown_status_code".to_string()));
                                scope.set_extra("status_code", serde_json::Value::String(res.status().to_string()));
                                scope.set_level(Some(sentry::Level::Error));
                            }, || {
                                sentry::capture_message(&format!("Unknown status code: {}", res.status()), sentry::Level::Error);
                            });
                            return Err(anyhow!("unknown status code"));
                        }
                    }
                },
                Err(e) => {
                    // Capture the error with network details
                    sentry::with_scope(|scope| {
                        scope.set_tag("operation", "assign_masternode");
                        scope.set_extra("url", serde_json::Value::String(_self.assign_masternode_path.clone()));
                        // Set error level
                        scope.set_level(Some(sentry::Level::Error));
                    }, || {
                        sentry::capture_message(&format!("Failed to request masternode assignment: {}", e), sentry::Level::Error);
                    });

                    return Err(anyhow!("get assign masternode failed"));
                }
            }
    }

    pub async fn refresh_token(self: Arc<Self>) -> Result<AuthTokens> {
        let _self = self.clone();
        let auth_tokens = self.clone().get_auth_tokens().await;

        // Add a breadcrumb before the request
        sentry::add_breadcrumb(sentry::Breadcrumb {
            category: Some("http".into()),
            message: Some(format!(
                "Refreshing token at {}",
                _self.refresh_token_path
            )),
            level: sentry::Level::Info,
            ..Default::default()
        });

        let client = reqwest::Client::new();
        match client
            .post(_self.refresh_token_path.clone())
            .json(&auth_tokens)
            .send()
            .await
        {
            Ok(res) => {
                // Add a breadcrumb for the response
                sentry::add_breadcrumb(sentry::Breadcrumb {
                    category: Some("http".into()),
                    message: Some(format!("Received token refresh response with status: {}", res.status())),
                    level: sentry::Level::Info,
                    ..Default::default()
                });

                match res.status() {
                    StatusCode::OK => match res.json::<SSORes>().await {
                        Ok(sso_res) => match sso_res.code {
                            1 => {
                                let auth_tokens = AuthTokens {
                                    access_token: sso_res.access_token.unwrap(),
                                    refresh_token: sso_res.refresh_token.unwrap(),
                                };
                                let mut auth_tokens_wlk = _self.auth_tokens.write().await;
                                *auth_tokens_wlk = auth_tokens.clone();
                                drop(auth_tokens_wlk);

                                Ok(auth_tokens.clone())
                            }
                            _ => {
                                // Capture the error with context
                                sentry::with_scope(|scope| {
                                    scope.set_tag("operation", "refresh_token");
                                    scope.set_extra("url", serde_json::Value::String(_self.refresh_token_path.clone()));
                                    scope.set_extra("error_type", serde_json::Value::String("invalid_response_code".to_string()));
                                    scope.set_extra("response_code", serde_json::Value::Number(serde_json::Number::from(sso_res.code)));
                                    scope.set_level(Some(sentry::Level::Error));
                                }, || {
                                    sentry::capture_message(&format!("Failed to refresh token: invalid response code {}", sso_res.code), sentry::Level::Error);
                                });
                                return Err(anyhow!("failed to refresh token"));
                            }
                        },
                        Err(e) => {
                            // Capture the JSON decoding error with context
                            sentry::with_scope(|scope| {
                                scope.set_tag("operation", "refresh_token");
                                scope.set_extra("url", serde_json::Value::String(_self.refresh_token_path.clone()));
                                scope.set_extra("error_type", serde_json::Value::String("json_decode_error".to_string()));
                                scope.set_extra("error_details", serde_json::Value::String(e.to_string()));
                                scope.set_level(Some(sentry::Level::Error));
                            }, || {
                                sentry::capture_message(&format!("Failed to decode refresh token JSON response: {}", e), sentry::Level::Error);
                            });
                            return Err(anyhow!("decode json failed"));
                        }
                    },
                    _ => {
                        // Capture unknown status code error
                        sentry::with_scope(|scope| {
                            scope.set_tag("operation", "refresh_token");
                            scope.set_extra("url", serde_json::Value::String(_self.refresh_token_path.clone()));
                            scope.set_extra("error_type", serde_json::Value::String("unexpected_status_code".to_string()));
                            scope.set_extra("status_code", serde_json::Value::String(res.status().to_string()));
                            scope.set_level(Some(sentry::Level::Error));
                        }, || {
                            sentry::capture_message(&format!("Unexpected status code during token refresh: {}", res.status()), sentry::Level::Error);
                        });
                        return Err(anyhow!("token refresh failed with unexpected status code"));
                    }
                }
            },
            Err(e) => {
                // Capture the network error with context
                sentry::with_scope(|scope| {
                    scope.set_tag("operation", "refresh_token");
                    scope.set_extra("url", serde_json::Value::String(_self.refresh_token_path.clone()));
                    scope.set_extra("error_type", serde_json::Value::String("network_error".to_string()));
                    scope.set_extra("error_details", serde_json::Value::String(e.to_string()));
                    scope.set_level(Some(sentry::Level::Error));
                }, || {
                    sentry::capture_message(&format!("Network error during token refresh: {}", e), sentry::Level::Error);
                });
                return Err(anyhow!("network error during token refresh"));
            }
        }
    }

    pub async fn get_auth_tokens(self: Arc<Self>) -> AuthTokens {
        let auth_tokens_rlk = self.auth_tokens.read().await;
        let auth_tokens = auth_tokens_rlk.clone();
        auth_tokens
    }

    async fn get_client(self: Arc<Self>) -> Client {
        let access_token = self.get_auth_tokens().await.access_token;

        Client::builder()
            .default_headers({
                let mut headers = reqwest::header::HeaderMap::new();
                headers.insert(
                    reqwest::header::AUTHORIZATION,
                    reqwest::header::HeaderValue::from_str(&format!("Bearer {}", access_token))
                        .unwrap(),
                );
                headers
            })
            .build()
            .unwrap()
    }
}