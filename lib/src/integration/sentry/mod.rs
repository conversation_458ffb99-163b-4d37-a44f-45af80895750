
use sentry::{ClientOptions, IntoDsn};

// TODO: update DSN
const RUST_DSN: &str = "https://<EMAIL>/4509359261089872";

pub fn init_sentry() -> sentry::ClientInitGuard {
    let options = ClientOptions {
        dsn: RUST_DSN.into_dsn().unwrap(),
        environment: Some("dev".to_string()),
        release: sentry::release_name!(),
        traces_sample_rate: 1.0,
        ..Default::default()
    };
    
    sentry::init(options)
}