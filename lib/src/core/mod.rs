pub mod address;
pub mod connector;
pub mod proxy_server;
pub mod quic;

use std::sync::Arc;

use crate::{
    core::{
        connector::DirectConnector,
        proxy_server::ProxyServer,
        quic::{quic_connect, OpenBidirectionalStream},
    },
    util::parse_cert_root_ca,
};

pub use crate::error::ProxyError;
use crate::types::{AuthTokens, MasternodeInfo};
use anyhow::Result;
use log::{error, info};
use rustls::Certificate;
use serde::{Deserialize, Serialize};
use serde_json;
use tokio::io::{AsyncReadExt, AsyncWriteExt};

#[derive(Serialize, Deserialize, PartialEq, Debug)]
pub enum ConnErrResult {
    InvalidAuth,
    SameIP,
    SpeedTestFailed,
    QuicFailed,
    SerializeAuthsFailed,
    OpenBidirectionalStreamFailed,
    StreamReadWriteFailed,
    ProxyServerErr(String),
    UnknownErr,
}

pub async fn start(
    auth_tokens: AuthTokens,
    masternode: MasternodeInfo,
) -> Result<Arc<ProxyServer<DirectConnector>>, ConnErrResult> {
    // Create a transaction for the entire connection process
    let transaction = sentry::start_transaction(
        sentry::TransactionContext::new(
            "masternode_connection",
            "connect_flow"
        )
    );

      // Set the transaction as current
      let _guard = sentry::Hub::current().configure_scope(|scope| {
        // Use a string for the transaction name
        scope.set_transaction(Some("masternode_connection"));
        scope.set_extra("masternode_peer_bind", serde_json::Value::String(masternode.peer_bind.clone()));
        scope.set_extra("has_root_ca", serde_json::Value::Bool(masternode.root_ca.is_some()));
    });

    // Create a span for the QUIC connection
    let quic_span = transaction.start_child(
        "quic_connect",
        "network"
    );

    let root_ca: Option<Certificate> = masternode
        .root_ca
        .clone()
        .and_then(|rca| parse_cert_root_ca(rca));

    let master_conn = {
        match quic_connect(&masternode.peer_bind, root_ca).await {
            Ok(conn) => {
                quic_span.set_status(sentry::protocol::SpanStatus::Ok);
                Ok(conn)
            },
            Err(e) => {
                quic_span.set_status(sentry::protocol::SpanStatus::InternalError);
                quic_span.set_data("error", serde_json::Value::String(e.to_string()));
                error!("quic failed err={}", e);
                Err(e)
            }
        }
    }
    .map_err(|_| ConnErrResult::QuicFailed)?;

    quic_span.finish(); // Finish the QUIC span

     // Create a span for opening the bidirectional stream
     let stream_span = transaction.start_child(
        "open_bidirectional_stream",
        "network"
    );

    let mut master_stream = match master_conn.open_bidirectional_stream().await {
        Ok(stream) => {
            stream_span.set_status(sentry::protocol::SpanStatus::Ok);
            stream
        },
        Err(e) => {
            stream_span.set_status(sentry::protocol::SpanStatus::InternalError);
            stream_span.set_data("error", serde_json::Value::String(e.to_string()));
            return Err(ConnErrResult::OpenBidirectionalStreamFailed);
        }
    };
    stream_span.finish();

    let mut buff = [0; 1500];

    // speed test
    for _ in 0..5000 {
        master_stream
            .write_all(&buff)
            .await
            .map_err(|_| ConnErrResult::StreamReadWriteFailed)?;
    }

    // auth
    let login_bytes =
        bincode::serialize(&auth_tokens).map_err(|_| ConnErrResult::SerializeAuthsFailed)?;
    master_stream
        .write_all(&login_bytes)
        .await
        .map_err(|_| ConnErrResult::StreamReadWriteFailed)?;

    // connection result
    let mut buf = [0u8; 1];
    master_stream
        .read_exact(&mut buf)
        .await
        .map_err(|_| ConnErrResult::StreamReadWriteFailed)?;
    info!("{}", buf[0]);
    match buf[0] {
        0 => {} // success
        1 => {
            // same ip connected already
            info!("same ip");
            return Err(ConnErrResult::SameIP);
        }
        2 => {
            // invalid access token
            info!("invalid auth");
            return Err(ConnErrResult::InvalidAuth);
        }
        3 => {
            // speed test failed
            info!("speed test failed");
            return Err(ConnErrResult::SpeedTestFailed);
        }
        _ => {
            // unknown error
            info!("unknown error");
            return Err(ConnErrResult::UnknownErr);
        }
    }
    info!("Connected to masternode!");

    // tokio::spawn(async move {
    //     loop {
    //         for _ in 0..5000 {
    //             if master_stream.read_exact(&mut buff).await.is_err() {
    //                 ()
    //             }
    //         }
    //         for _ in 0..5000 {
    //             _ = master_stream
    //                 .write_all(&buff)
    //                 .await;
    //         }
    //     }
    // });

    let server = Arc::new(ProxyServer::new(DirectConnector, master_conn));
    // if let Err(e) = server.clone().run().await {
    //     error!("server error: {}", e);
    //     return Err(ConnErrResult::ProxyServerErr(e.to_string()));
    // }

    transaction.finish();
    Ok(server)
}
