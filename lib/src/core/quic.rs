use anyhow::Result;
use async_trait::async_trait;
use log::info;
use quinn::{Connection, Endpoint, RecvStream, SendStream, ServerConfig, TransportConfig};
use rustls::{Certificate, OwnedTrustAnchor, PrivateKey, RootCertStore};
use rustls_pemfile::{certs, pkcs8_private_keys};
use sentry::Level;
use std::{
    fs::{self, File},
    io::{self, BufReader},
    pin::Pin,
    sync::Arc,
    task::{Context, Poll},
};
use tokio::io::{AsyncRead, AsyncWrite, ReadBuf};

pub fn configure_server(certs_path: &str, keys_path: &str) -> Result<ServerConfig> {
    let cert_file = &mut BufReader::new(File::open(certs_path)?);
    let key_file = &mut BufReader::new(File::open(keys_path)?);

    let cert_chain = certs(cert_file).unwrap();
    let cert_chain = cert_chain
        .iter()
        .map(|x| Certificate(x.clone()))
        .collect::<Vec<Certificate>>();
    let mut keys = pkcs8_private_keys(key_file).unwrap();
    let mut server_crypto = rustls::ServerConfig::builder()
        .with_safe_defaults()
        .with_no_client_auth()
        .with_single_cert(cert_chain, PrivateKey(keys.remove(0)))
        .unwrap();
    server_crypto.alpn_protocols = ALPN_QUIC_HTTP.iter().map(|&x| x.into()).collect();
    let mut server_config = quinn::ServerConfig::with_crypto(Arc::new(server_crypto));
    server_config.migration(true);
    Arc::get_mut(&mut server_config.transport)
        .unwrap()
        .max_concurrent_uni_streams(0_u8.into())
        .keep_alive_interval(Some(std::time::Duration::from_secs(5)))
        .max_idle_timeout(Some(std::time::Duration::from_secs(10).try_into()?));
    Ok(server_config)
}

pub fn configure_client(self_signed_root_ca: Option<Certificate>) -> Result<quinn::ClientConfig> {
    let mut root_store = RootCertStore::empty();
    root_store.add_trust_anchors(webpki_roots::TLS_SERVER_ROOTS.iter().map(|ta| {
        OwnedTrustAnchor::from_subject_spki_name_constraints(
            ta.subject,
            ta.spki,
            ta.name_constraints,
        )
    }));

    if let Some(root_ca) = self_signed_root_ca {
        _ = root_store.add(&root_ca);
    }

    let mut client_crypto = rustls::ClientConfig::builder()
        .with_safe_defaults()
        .with_root_certificates(root_store)
        .with_no_client_auth();
    client_crypto.alpn_protocols = ALPN_QUIC_HTTP.iter().map(|&x| x.into()).collect();
    let mut config = quinn::ClientConfig::new(Arc::new(client_crypto));
    let mut transport = TransportConfig::default();
    transport.keep_alive_interval(Some(std::time::Duration::from_secs(5)));
    transport.max_idle_timeout(Some(std::time::Duration::from_secs(10).try_into()?));
    config.transport_config(Arc::new(transport));
    Ok(config)
}

#[allow(unused)]
#[allow(clippy::field_reassign_with_default)] // https://github.com/rust-lang/rust-clippy/issues/6527
pub fn gen_certificates(names: Vec<String>) -> Result<()> {
    let cert = rcgen::generate_simple_self_signed(names).unwrap();
    let cert_der = cert.serialize_der().unwrap();
    fs::write("./cert.der".to_string(), &cert_der).unwrap();
    let priv_key = cert.serialize_private_key_der();
    fs::write("./key.der".to_string(), &priv_key).unwrap();
    let key = rustls::PrivateKey(priv_key);
    let cert = vec![rustls::Certificate(cert_der.clone())];
    let mut server_crypto = rustls::ServerConfig::builder()
        .with_safe_defaults()
        .with_no_client_auth()
        .with_single_cert(cert, key)?;
    server_crypto.alpn_protocols = ALPN_QUIC_HTTP.iter().map(|&x| x.into()).collect();
    Ok(())
}

#[allow(unused)]
pub const ALPN_QUIC_HTTP: &[&[u8]] = &[b"hq-29"];
#[async_trait]
pub trait AcceptBidirectionalStream {
    async fn accept_bidirectional_stream(&self) -> io::Result<BidirectionalStream>;
}
#[async_trait]
impl AcceptBidirectionalStream for Connection {
    async fn accept_bidirectional_stream(self: &Connection) -> io::Result<BidirectionalStream> {
        let (send, recv) = self.accept_bi().await?;
        Ok(BidirectionalStream {
            write: send,
            read: recv,
        })
    }
}
#[async_trait]
pub trait OpenBidirectionalStream {
    async fn open_bidirectional_stream(&self) -> io::Result<BidirectionalStream>;
}
#[async_trait]
impl OpenBidirectionalStream for Connection {
    async fn open_bidirectional_stream(self: &Connection) -> io::Result<BidirectionalStream> {
        let (send, recv) = self.open_bi().await?;
        Ok(BidirectionalStream {
            write: send,
            read: recv,
        })
    }
}
pub struct BidirectionalStream {
    pub write: SendStream,
    pub read: RecvStream,
}
impl AsyncRead for BidirectionalStream {
    fn poll_read(
        self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &mut ReadBuf<'_>,
    ) -> Poll<io::Result<()>> {
        let pin_mut = Pin::new(&mut self.get_mut().read);
        RecvStream::poll_read(pin_mut, cx, buf)
    }
}

impl AsyncWrite for BidirectionalStream {
    fn poll_write(
        self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<io::Result<usize>> {
        let pin_mut = Pin::new(&mut self.get_mut().write);
        SendStream::poll_write(pin_mut, cx, buf)
    }
    fn poll_flush(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<io::Result<()>> {
        let pin_mut = Pin::new(&mut self.get_mut().write);
        SendStream::poll_flush(pin_mut, cx)
    }
    fn poll_shutdown(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<io::Result<()>> {
        SendStream::poll_finish(&mut self.get_mut().write, cx).map_err(Into::into)
    }
}

impl BidirectionalStream {
    pub fn new(write: SendStream, read: RecvStream) -> Self {
        Self { write, read }
    }
    pub fn split(self) -> (SendStream, RecvStream) {
        (self.write, self.read)
    }
}

pub async fn quic_connect(
    addr: &str,
    self_signed_root_ca: Option<Certificate>,
) -> Result<Connection> {
    let domain = addr
        .split(':')
        .next()
        .ok_or(anyhow::anyhow!("Invalid address, no port specified"))?;
    let socket_addr = tokio::net::lookup_host(addr)
        .await?
        .filter(|x| x.is_ipv4())
        .next()
        .ok_or(anyhow::anyhow!("No ipv4 found"))?;
    let mut endpoint = Endpoint::client("0.0.0.0:0".parse()?)?;
    let config = configure_client(self_signed_root_ca.clone())?;
    info!("configuring client");
    endpoint.set_default_client_config(config);
    match endpoint.connect(socket_addr, domain) {
        Ok(connecting) => {
            match connecting.await {
                Ok(connection) => Ok(connection),
                Err(e) => {
                    let err = anyhow::anyhow!("QUIC connection failed: {}", e);

                    // Add detailed context to the error
                    sentry::with_scope(|scope| {
                        scope.set_tag("connection_stage", "quic_handshake");
                        scope.set_extra("target_addr", serde_json::Value::String(addr.to_string()));
                        scope.set_extra("resolved_addr", serde_json::Value::String(socket_addr.to_string()));
                        scope.set_extra("domain", serde_json::Value::String(domain.to_string()));
                        scope.set_extra("has_root_ca", serde_json::Value::Bool(self_signed_root_ca.is_some()));
                        scope.set_level(Some(Level::Error));
                    }, || {
                        // Log the error message directly instead of using capture_error
                        sentry::capture_message(&format!("QUIC connection failed: {}", e), Level::Error);
                    });

                    Err(err)
                }
            }
        },
        Err(e) => {
            let err = anyhow::anyhow!("Failed to initiate QUIC connection: {}", e);

            // Use capture_message instead of capture_error
            sentry::capture_message(&format!("Failed to initiate QUIC connection: {}", e), Level::Error);

            Err(err)
        }
    }
}
