use dpn_peernode_lib::{
    daemon::{PeerNodeDaemon, PeerNodeDaemonConfig},
    types::AuthTokens,
};
use std::sync::Arc;
use tokio::runtime::Runtime;

pub fn run_peer_node(
    webserver_addr: String,
    webserver_port: u16,
    admin_addr: String,
    access_token: String,
    refresh_token: String,
) {
    let daemon = Arc::new(PeerNodeDaemon::new(PeerNodeDaemonConfig {
        admin_addr,
        webserver_addr,
        webserver_port,
        auth_tokens: AuthTokens {
            access_token,
            refresh_token,
        },
    }));

    let rt = Runtime::new().unwrap();
    rt.block_on(async {
        _ = daemon.run_with_websocket().await;
    });
}
