[package]
name = "dpn_peernode"
version = "0.1.3"
authors = ["daibo"]
edition = "2021"
readme = "README.md"
keywords = ["proxy", "socks5"]

[[bin]]
name = "peernode"
path = "src/main.rs"

[workspace]
members = ["bridge", "wasm_bridge"]

[dependencies]
dpn_peernode_lib = { path = "lib" }
serde_json = { version = "1.0.91", default-features = false }
tokio = { version = "1.21.2", default-features = false }
serde = { version = "1.0", default-features = false }
anyhow = "1.0.62"
serde_yaml = "0.9.16"
env_logger = "0.10.0"
sentry = { version = "0.31.8", features = ["anyhow", "log", "reqwest", "rustls", "tokio"] }
